import request from '@/utils/request'

// 查询TRfmMaster列表
export function listTRfmMaster(query) {
  return request({
    url: '/api/v1/t-rfm-master',
    method: 'get',
    params: query
  })
}

// 查询TRfmMaster详细
export function getTRfmMaster(id) {
  return request({
    url: '/api/v1/t-rfm-master/' + id,
    method: 'get'
  })
}

// 新增TRfmMaster
export function addTRfmMaster(data) {
  return request({
    url: '/api/v1/t-rfm-master',
    method: 'post',
    data: data
  })
}

// 修改TRfmMaster
export function updateTRfmMaster(data) {
  return request({
    url: '/api/v1/t-rfm-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TRfmMaster
export function delTRfmMaster(data) {
  return request({
    url: '/api/v1/t-rfm-master',
    method: 'delete',
    data: data
  })
}

