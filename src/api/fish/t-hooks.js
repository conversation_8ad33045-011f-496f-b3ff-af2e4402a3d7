import request from '@/utils/request'

// 查询THooks列表
export function listTHooks(query) {
  return request({
    url: '/api/v1/t-hooks',
    method: 'get',
    params: query
  })
}

// 查询THooks详细
export function getTHooks(id) {
  return request({
    url: '/api/v1/t-hooks/' + id,
    method: 'get'
  })
}

// 新增THooks
export function addTHooks(data) {
  return request({
    url: '/api/v1/t-hooks',
    method: 'post',
    data: data
  })
}

// 修改THooks
export function updateTHooks(data) {
  return request({
    url: '/api/v1/t-hooks/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除THooks
export function delTHooks(data) {
  return request({
    url: '/api/v1/t-hooks',
    method: 'delete',
    data: data
  })
}

