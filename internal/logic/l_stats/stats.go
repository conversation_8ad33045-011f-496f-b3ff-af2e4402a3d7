package l_stats

import (
	"context"
	"statssrv/config"
	"statssrv/internal/model"
	"statssrv/internal/repository"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"github.com/sirupsen/logrus"
)

const (
	NOT_FOUND_TARGET = -2
)

// 统计数据
func ScanCfg(event *commonPB.EventCommon, cfgs map[int64]*cmodel.Stats) ([]*model.TStats, error) {
	arr := make([]*model.TStats, 0)

	var ok bool
	for _, cfg := range cfgs {
		// 事件类型符合
		if cfg.Event != int64(event.EventType) {
			continue
		}
		// 筛选条件符合
		flag := true
		for _, cond := range cfg.Conds {
			checkVal := cond.Value
			eventVal := event.IntData[cond.Label]
			if ok = operate.CheckVal(commonPB.VAL_OPERATE(cond.Operate), eventVal, checkVal); !ok {
				flag = false
				break
			}
		}
		if !flag {
			continue
		}
		// 特殊规则
		switch cfg.Type {
		case config.STATS_CFG_KEY_TYPE_FISH:
			// 该条件为检查 鱼 - 品种
			if ok = operate.CheckVal(commonPB.VAL_OPERATE_VO_EQUAL, event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_SPECIAL)], cfg.Target); !ok {
				continue
			}
		}

		// 构造变化
		var val int64 = 1 // 默认值
		if cfg.Field != 0 {
			val, ok = event.IntData[int32(cfg.Field)]
			if !ok {
				logrus.Warnf("stats config:[%v] valId:%v not found", cfg.Id, cfg.Field)
				continue
			}
		}
		stats := &model.TStats{
			AddType:   commonPB.SUM_ADD(cfg.Update),
			CondKey:   cfg.Id,
			PlayerId:  event.GetPlayerId(),
			ProductId: event.GetProductId(),
			Val:       val,
		}
		arr = append(arr, stats)
	}

	return arr, nil

}

// ScanRuleCheck 检查统计规则
func ScanRuleCheck(event *commonPB.EventCommon, cfgs map[int64]*cmodel.StatsRule) ([]*model.TStatsRule, error) {
	arr := make([]*model.TStatsRule, 0)

	for _, cfg := range cfgs {
		// 过滤条件

		// 过滤目标
		target := filterTarget(event, cfg)
		if target == NOT_FOUND_TARGET {
			continue
		}

		// 默认变化值
		var val int64 = 1
		// 尝试提取数据
		if cfg.Field != 0 {
			valTmp, ok := event.IntData[int32(cfg.Field)]
			if !ok {
				continue
			}
			val = valTmp
		}

		statsRule := &model.TStatsRule{
			PlayerId: event.GetPlayerId(),
			AddType:  commonPB.SUM_ADD(cfg.AddRule),
			Typ:      cfg.Typ,
			Target:   target,
			Field:    cfg.Field,
			Val:      val,
		}
		arr = append(arr, statsRule)
	}

	return arr, nil
}

// 根据规则过滤目标 -1表示不满足条件
func filterTarget(event *commonPB.EventCommon, cfg *cmodel.StatsRule) int64 {
	// -1：全部
	// 0：任意
	switch commonPB.STATS_TYPE(cfg.Typ) {
	case commonPB.STATS_TYPE_ST_FISH:
		return doFilterTarget(event, cfg.Target, int32(commonPB.EVENT_INT_KEY_EIK_FISH_SPECIAL))
	default:
		// key 默认找不到
		return NOT_FOUND_TARGET
	}
}

// 过滤目标
func doFilterTarget(event *commonPB.EventCommon, target int64, key int32) int64 {
	switch target {
	case config.STATS_RULE_KEY_TYPE_ANY:
		return event.IntData[key]
	case config.STATS_RULE_KEY_TYPE_ALL:
		return config.STATS_RULE_KEY_TYPE_ALL
	default:
		if event.IntData[key] == target {
			return target
		} else {
			return NOT_FOUND_TARGET
		}
	}
}

// GetStatsRules 获取统计规则
func GetStatsRules(ctx context.Context, playerId uint64, typ int32, field int64, target int64, addType commonPB.SUM_ADD) ([]*model.TStatsRule, error) {
	rules, err := repository.GetPlayerStatsRule(ctx, playerId)
	if err != nil {
		return nil, err
	}
	rtnList := make([]*model.TStatsRule, 0)
	for _, rule := range rules {
		if typ != config.STATS_RULE_KEY_TYPE_ANY && rule.Typ != typ {
			continue
		}
		if field != config.STATS_RULE_KEY_TYPE_ANY && rule.Field != field {
			continue
		}
		if target != config.STATS_RULE_KEY_TYPE_ANY && rule.Target != target {
			continue
		}
		if addType != config.STATS_RULE_KEY_TYPE_ANY && rule.AddType != addType {
			continue
		}
		rtnList = append(rtnList, rule)
	}

	return rtnList, nil
}
