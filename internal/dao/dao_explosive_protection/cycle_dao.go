package dao_explosive_protection

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// CycleDAO 活动周期数据访问对象
type CycleDAO struct{}

// NewCycleDAO 创建新的周期DAO
func NewCycleDAO() *CycleDAO {
	return &CycleDAO{}
}

// GetCurrentCycle 获取当前活动周期
func (dao *CycleDAO) GetCurrentCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	key := config.ExplosiveProtectionCurrentCycleKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	data, err := redisCli.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// 当前周期不存在，需要创建
			return nil, nil
		}
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}
	
	cycle := &model.ActivityCycle{}
	if err := cycle.FromJSON(data); err != nil {
		return nil, fmt.Errorf("解析周期数据失败: %w", err)
	}
	
	return cycle, nil
}

// CreateNewCycle 创建新的活动周期
func (dao *CycleDAO) CreateNewCycle(ctx context.Context, activityId int64, cycleDays int32) (*model.ActivityCycle, error) {
	// 使用分布式锁防止并发创建
	lockKey := config.ExplosiveProtectionCycleLockKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	// 尝试获取锁
	lockValue := fmt.Sprintf("%d_%d", time.Now().UnixNano(), activityId)
	acquired, err := redisCli.SetNX(ctx, lockKey, lockValue, config.ExplosiveProtectionCycleLockExpire).Result()
	if err != nil {
		return nil, fmt.Errorf("获取分布式锁失败: %w", err)
	}
	if !acquired {
		return nil, fmt.Errorf("其他进程正在创建周期，请稍后重试")
	}
	
	// 确保释放锁
	defer func() {
		redisCli.Del(ctx, lockKey)
	}()
	
	// 再次检查是否已经有当前周期（双重检查）
	existingCycle, err := dao.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}
	if existingCycle != nil {
		return existingCycle, nil
	}
	
	// 获取下一个周期ID
	nextCycleId, err := dao.getNextCycleId(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("获取下一个周期ID失败: %w", err)
	}
	
	// 计算周期时间
	now := time.Now()
	startTime := now.Unix()
	endTime := now.Add(time.Duration(cycleDays) * 24 * time.Hour).Unix()
	
	// 创建新周期
	newCycle := model.NewActivityCycle(nextCycleId, startTime, endTime)
	
	// 保存到Redis
	if err := dao.saveCurrentCycle(ctx, activityId, newCycle); err != nil {
		return nil, fmt.Errorf("保存当前周期失败: %w", err)
	}
	
	// 保存到历史记录
	if err := dao.saveCycleHistory(ctx, activityId, newCycle); err != nil {
		logrus.Errorf("保存周期历史失败: %v", err)
		// 历史记录失败不影响主流程
	}
	
	logrus.Infof("成功创建新的活动周期: activityId=%d, cycleId=%d, startTime=%d, endTime=%d", 
		activityId, newCycle.CycleId, newCycle.StartTime, newCycle.EndTime)
	
	return newCycle, nil
}

// CheckAndCreateCycleIfNeeded 检查并在需要时创建新周期
func (dao *CycleDAO) CheckAndCreateCycleIfNeeded(ctx context.Context, activityId int64, cycleDays int32) (*model.ActivityCycle, error) {
	currentCycle, err := dao.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}
	
	// 如果没有当前周期或当前周期已过期，创建新周期
	if currentCycle == nil || currentCycle.IsExpired() {
		if currentCycle != nil {
			// 标记旧周期为结束状态
			currentCycle.Status = 2
			dao.saveCycleHistory(ctx, activityId, currentCycle)
		}
		
		return dao.CreateNewCycle(ctx, activityId, cycleDays)
	}
	
	return currentCycle, nil
}

// getNextCycleId 获取下一个周期ID
func (dao *CycleDAO) getNextCycleId(ctx context.Context, activityId int64) (int32, error) {
	historyKey := config.ExplosiveProtectionCycleHistoryKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	// 从历史记录中获取最大的周期ID
	result, err := redisCli.ZRevRange(ctx, historyKey, 0, 0).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	
	if len(result) == 0 {
		// 没有历史记录，从1开始
		return 1, nil
	}
	
	// 解析最大的周期ID
	maxCycleIdStr := result[0]
	maxCycleId, err := strconv.ParseInt(maxCycleIdStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("解析最大周期ID失败: %w", err)
	}
	
	return int32(maxCycleId) + 1, nil
}

// saveCurrentCycle 保存当前周期到Redis
func (dao *CycleDAO) saveCurrentCycle(ctx context.Context, activityId int64, cycle *model.ActivityCycle) error {
	key := config.ExplosiveProtectionCurrentCycleKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	data, err := cycle.ToJSON()
	if err != nil {
		return err
	}
	
	// 设置过期时间为周期时长+1天
	expireDuration := time.Duration(cycle.EndTime-cycle.StartTime)*time.Second + 24*time.Hour
	
	return redisCli.Set(ctx, key, data, expireDuration).Err()
}

// saveCycleHistory 保存周期到历史记录
func (dao *CycleDAO) saveCycleHistory(ctx context.Context, activityId int64, cycle *model.ActivityCycle) error {
	historyKey := config.ExplosiveProtectionCycleHistoryKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	data, err := cycle.ToJSON()
	if err != nil {
		return err
	}
	
	// 使用周期ID作为score，周期数据作为member
	return redisCli.ZAdd(ctx, historyKey, &redis.Z{
		Score:  float64(cycle.CycleId),
		Member: data,
	}).Err()
}

// GetCycleHistory 获取周期历史记录
func (dao *CycleDAO) GetCycleHistory(ctx context.Context, activityId int64, limit int64) ([]*model.ActivityCycle, error) {
	historyKey := config.ExplosiveProtectionCycleHistoryKey(activityId)
	redisCli := redisx.GetActivityCli()
	
	// 按score倒序获取最近的几个周期
	result, err := redisCli.ZRevRange(ctx, historyKey, 0, limit-1).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return []*model.ActivityCycle{}, nil
		}
		return nil, err
	}
	
	cycles := make([]*model.ActivityCycle, 0, len(result))
	for _, data := range result {
		cycle := &model.ActivityCycle{}
		if err := cycle.FromJSON(data); err != nil {
			logrus.Errorf("解析周期历史数据失败: %v", err)
			continue
		}
		cycles = append(cycles, cycle)
	}
	
	return cycles, nil
}
