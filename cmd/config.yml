# server
rpc_server_name: login
rpc_port: 11301

# 端口号
http_port: 21301

# 日志相关
log_level: trace
log_write: true[[
log_dir: ../../logs/login
log_json: false

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  gateway:
    addr: 192.168.1.58:6379
    passwd: 8888

  player:
    addr: 192.168.1.58:6379
    passwd: 8888

consul_addr: 192.168.1.58:8500

nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

# 消息路由表,在router.yml文件配置
message_router:
  gate: [1000, 1099]
  login: [1100, 1199]

auth_key: keepfancy

rpc_server_tags: normal

# 地理位置数据库路径
geo_db_path: ../GeoLite2-City.mmdb

kafka-producer:
  brokers: ["192.168.1.51:9092"]
  timeout: 10
